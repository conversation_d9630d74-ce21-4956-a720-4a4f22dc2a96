{"name": "Meta_Learnings", "private": true, "version": "1.0.0", "description": "Meta_Learnings", "scripts": {"lint": "eslint **/{aura,lwc}/**/*.js", "test": "npm run test:unit", "test:unit": "sfdx-lwc-jest", "test:unit:watch": "sfdx-lwc-jest --watch", "test:unit:debug": "sfdx-lwc-jest --debug", "test:unit:coverage": "sfdx-lwc-jest --coverage", "prettier": "prettier --write \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prettier:verify": "prettier --check \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "postinstall": "husky install", "precommit": "lint-staged"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.33.0", "@lwc/eslint-plugin-lwc": "^2.0.0", "@prettier/plugin-xml": "^3.2.2", "@sa11y/jest": "^3.1.0", "@salesforce/eslint-config-lwc": "^3.7.2", "@salesforce/eslint-plugin-aura": "^2.0.0", "@salesforce/eslint-plugin-lightning": "^1.0.0", "@salesforce/sfdx-lwc-jest": "^7.1.0", "@types/eslint__js": "^8.42.3", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^28.8.1", "globals": "^16.3.0", "husky": "^9.1.5", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "prettier-plugin-apex": "^2.0.1", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1"}, "lint-staged": {"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}": ["prettier --write"], "**/{aura,lwc}/**/*.js": ["eslint"]}}