#!/bin/bash

echo "Validating Excel Importer deployment package..."

# Check if all required files exist
echo "Checking Apex classes..."
if [ ! -f "src/classes/ExcelImportController.cls" ]; then
    echo "ERROR: Missing ExcelImportController.cls"
    exit 1
fi

if [ ! -f "src/classes/ExcelImportController.cls-meta.xml" ]; then
    echo "ERROR: Missing ExcelImportController.cls-meta.xml"
    exit 1
fi

if [ ! -f "src/classes/ExcelImportControllerTests.cls" ]; then
    echo "ERROR: Missing ExcelImportControllerTests.cls"
    exit 1
fi

if [ ! -f "src/classes/ExcelImportControllerTests.cls-meta.xml" ]; then
    echo "ERROR: Missing ExcelImportControllerTests.cls-meta.xml"
    exit 1
fi

echo "✓ Apex classes OK"

# Check LWC components
echo "Checking LWC components..."
components=("excelImporter" "fileUploadStep" "fieldMapper" "dataPreview" "importResults")

for component in "${components[@]}"; do
    if [ ! -d "src/lwc/$component" ]; then
        echo "ERROR: Missing LWC component directory: $component"
        exit 1
    fi
    
    if [ ! -f "src/lwc/$component/$component.js" ]; then
        echo "ERROR: Missing $component.js"
        exit 1
    fi
    
    if [ ! -f "src/lwc/$component/$component.html" ]; then
        echo "ERROR: Missing $component.html"
        exit 1
    fi
    
    if [ ! -f "src/lwc/$component/$component.js-meta.xml" ]; then
        echo "ERROR: Missing $component.js-meta.xml"
        exit 1
    fi
done

echo "✓ LWC components OK"

# Check package.xml
if [ ! -f "excelImporter-package.xml" ]; then
    echo "ERROR: Missing package.xml"
    exit 1
fi

echo "✓ Package manifest OK"

echo ""
echo "All files validated successfully!"
echo "Ready to deploy with: sfdx force:source:deploy -x excelImporter-package.xml"