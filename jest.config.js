const { jestConfig } = require('@salesforce/sfdx-lwc-jest/config');

const setupFilesAfterEnv = jestConfig.setupFilesAfterEnv || [];
setupFilesAfterEnv.push('<rootDir>/jest-sa11y-setup.js');

module.exports = {
    ...jestConfig,
    testRegex: '/__tests__/.*.test.ts$',
    moduleNameMapper: {
        '^@salesforce/apex$': '<rootDir>/test/jest-mocks/apex',
        '^lightning/navigation$':
            '<rootDir>/test/jest-mocks/lightning/navigation',
        '^lightning/messageService$':
            '<rootDir>/test/jest-mocks/lightning/messageService',
        '^lightning/empApi$':
            '<rootDir>/test/jest-mocks/lightning/empApi'
    },
    setupFilesAfterEnv
};