@echo off
REM Deploy Excel Importer to Salesforce

echo Deploying Excel Importer components to Salesforce...

REM Deploy Apex classes
echo Deploying Apex classes...
sfdx force:source:deploy -p src/classes/ExcelImportController.cls
sfdx force:source:deploy -p src/classes/ExcelImportControllerTests.cls

REM Deploy LWC components
echo Deploying LWC components...
sfdx force:source:deploy -p src/lwc/excelImporter
sfdx force:source:deploy -p src/lwc/fileUploadStep
sfdx force:source:deploy -p src/lwc/fieldMapper
sfdx force:source:deploy -p src/lwc/dataPreview
sfdx force:source:deploy -p src/lwc/importResults

REM Or deploy all at once using package.xml
echo Deploying all components using package.xml...
sfdx force:source:deploy -x package.xml

echo Deployment complete!

REM Optional: Open org to verify
echo Opening Salesforce org...
sfdx force:org:open

pause