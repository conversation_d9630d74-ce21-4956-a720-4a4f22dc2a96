@echo off
echo Validating Excel Importer deployment package...

REM Check if all required files exist
echo Checking Apex classes...
if not exist "src\classes\ExcelImportController.cls" (
    echo ERROR: Missing ExcelImportController.cls
    pause
    exit /b 1
)

if not exist "src\classes\ExcelImportController.cls-meta.xml" (
    echo ERROR: Missing ExcelImportController.cls-meta.xml
    pause
    exit /b 1
)

if not exist "src\classes\ExcelImportControllerTests.cls" (
    echo ERROR: Missing ExcelImportControllerTests.cls
    pause
    exit /b 1
)

if not exist "src\classes\ExcelImportControllerTests.cls-meta.xml" (
    echo ERROR: Missing ExcelImportControllerTests.cls-meta.xml
    pause
    exit /b 1
)

echo ✓ Apex classes OK

REM Check LWC components
echo Checking LWC components...
set components=excelImporter fileUploadStep fieldMapper dataPreview importResults

for %%c in (%components%) do (
    if not exist "src\lwc\%%c" (
        echo ERROR: Missing LWC component directory: %%c
        pause
        exit /b 1
    )
    
    if not exist "src\lwc\%%c\%%c.js" (
        echo ERROR: Missing %%c.js
        pause
        exit /b 1
    )
    
    if not exist "src\lwc\%%c\%%c.html" (
        echo ERROR: Missing %%c.html
        pause
        exit /b 1
    )
    
    if not exist "src\lwc\%%c\%%c.js-meta.xml" (
        echo ERROR: Missing %%c.js-meta.xml
        pause
        exit /b 1
    )
)

echo ✓ LWC components OK

REM Check package.xml
if not exist "excelImporter-package.xml" (
    echo ERROR: Missing package.xml
    pause
    exit /b 1
)

echo ✓ Package manifest OK

echo.
echo All files validated successfully!
echo Ready to deploy with: sfdx force:source:deploy -x excelImporter-package.xml
pause