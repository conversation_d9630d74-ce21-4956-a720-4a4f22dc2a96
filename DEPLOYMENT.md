# Deployment Instructions

## Prerequisites

1. Install Salesforce CLI
2. Authenticate to your Salesforce org:
   ```bash
   sfdx force:auth:web:login -d -a MyOrg
   ```

## Deployment Options

### Option 1: Deploy using package.xml (Recommended)
```bash
sfdx force:source:deploy -x package.xml
```

### Option 2: Deploy individual components
```bash
# Deploy Apex classes
sfdx force:source:deploy -p src/classes/ExcelImportController.cls
sfdx force:source:deploy -p src/classes/ExcelImportControllerTests.cls

# Deploy LWC components
sfdx force:source:deploy -p src/lwc/excelImporter
```

### Option 3: Use the deployment script
```bash
# On Windows
deploy.bat

# On Unix/Linux/Mac
bash deploy.sh
```

## Post-Deployment

1. **Add the component to a Lightning Page**:
   - Go to Setup > Lightning App Builder
   - Create or edit a Lightning Page
   - Add the "Excel Importer" component from the custom components section

2. **Configure Component Properties**:
   - Title: Excel Data Importer (default)
   - Show Object Selection: true (default)
   - Allowed File Types: .csv,.xlsx,.xls (default)

3. **Test the Component**:
   - Prepare a sample CSV file with headers matching Salesforce field names
   - Upload the file through the component
   - Map fields and import data

## Troubleshooting

If deployment fails:
1. Check if you have the correct API version (64.0)
2. Ensure you have the necessary permissions in the org
3. Verify all metadata XML files are present

## File Structure

```
src/
├── classes/
│   ├── ExcelImportController.cls
│   ├── ExcelImportController.cls-meta.xml
│   ├── ExcelImportControllerTests.cls
│   └── ExcelImportControllerTests.cls-meta.xml
└── lwc/
    ├── excelImporter/
    ├── fileUploadStep/
    ├── fieldMapper/
    ├── dataPreview/
    └── importResults/
```