<template>
    <div class="import-results">
        <!-- Results Summary -->
        <div class="slds-m-bottom_large">
            <lightning-card icon-name={summaryIcon}>
                <div slot="title">
                    <h2 class="slds-text-heading_medium">{summaryTitle}</h2>
                </div>
                <div class="slds-p-around_medium">
                    <div class="results-grid">
                        <div class="result-card success">
                            <div class="result-icon">
                                <lightning-icon icon-name="utility:success" size="large"></lightning-icon>
                            </div>
                            <div class="result-details">
                                <div class="result-number">{importResult.successCount}</div>
                                <div class="result-label">Records Created</div>
                            </div>
                        </div>
                        <div class="result-card error" if:true={hasErrors}>
                            <div class="result-icon">
                                <lightning-icon icon-name="utility:error" size="large"></lightning-icon>
                            </div>
                            <div class="result-details">
                                <div class="result-number">{importResult.errorCount}</div>
                                <div class="result-label">Errors</div>
                            </div>
                        </div>
                        <div class="result-card total">
                            <div class="result-icon">
                                <lightning-icon icon-name="utility:summary" size="large"></lightning-icon>
                            </div>
                            <div class="result-details">
                                <div class="result-number">{totalRecords}</div>
                                <div class="result-label">Total Processed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Error Details -->
        <div if:true={hasErrors} class="slds-m-bottom_large">
            <lightning-card title="Error Details" icon-name="standard:log_event">
                <div class="slds-p-around_medium">
                    <div class="error-table-container">
                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th scope="col">Row #</th>
                                    <th scope="col">Error Message</th>
                                    <th scope="col">Preview Data</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={errorRecords} for:item="error">
                                    <tr key={error.rowNumber} class="slds-hint-parent">
                                        <td>{error.rowNumber}</td>
                                        <td>
                                            <span class="error-text">{error.errorMessage}</span>
                                        </td>
                                        <td>
                                            <lightning-button
                                                label="View Data"
                                                variant="base"
                                                onclick={handleViewData}
                                                data-row={error.rowNumber}>
                                            </lightning-button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Success Records -->
        <div if:true={hasSuccessRecords} class="slds-m-bottom_large">
            <lightning-card title="Successfully Imported Records" icon-name="standard:record">
                <div class="slds-p-around_medium">
                    <p class="slds-m-bottom_small">
                        Click on a record ID to view it in Salesforce:
                    </p>
                    <div class="success-records-list">
                        <template for:each={successRecords} for:item="record">
                            <div key={record.recordId} class="record-item">
                                <lightning-button
                                    label={record.recordId}
                                    variant="base"
                                    onclick={handleViewRecord}
                                    data-id={record.recordId}>
                                </lightning-button>
                                <span class="row-number">Row {record.rowNumber}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Data Preview Modal -->
        <template if:true={showDataModal}>
            <div role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_large">
                <div class="slds-modal__container">
                    <div class="slds-modal__header">
                        <h2 class="slds-text-heading_medium slds-hyphenate">Row {selectedRowNumber} Data</h2>
                        <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" onclick={handleCloseModal}>
                            <lightning-icon icon-name="utility:close" size="small"></lightning-icon>
                        </button>
                    </div>
                    <div class="slds-modal__content slds-p-around_medium">
                        <div class="data-preview-grid">
                            <template for:each={selectedRowData} for:item="field">
                                <div key={field.key} class="data-field">
                                    <div class="field-label">{field.key}:</div>
                                    <div class="field-value">{field.value}</div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="slds-modal__footer">
                        <lightning-button
                            label="Close"
                            variant="neutral"
                            onclick={handleCloseModal}>
                        </lightning-button>
                    </div>
                </div>
            </div>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </template>

        <!-- Action Buttons -->
        <div class="slds-m-top_large">
            <lightning-button
                label="Download Results"
                variant="brand"
                onclick={handleDownload}
                class="slds-m-right_small">
            </lightning-button>
            <lightning-button
                label="Import Another File"
                variant="neutral"
                onclick={handleReset}>
            </lightning-button>
        </div>
    </div>
</template>