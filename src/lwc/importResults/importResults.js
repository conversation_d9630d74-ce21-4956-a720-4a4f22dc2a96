import { LightningElement, api, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';

export default class ImportResults extends NavigationMixin(LightningElement) {
    @api importResult;

    @track showDataModal = false;
    @track selectedRowNumber = null;
    @track selectedRowData = [];

    // Getters
    get summaryTitle() {
        return this.importResult.isSuccess ? 'Import Completed' : 'Import Failed';
    }

    get summaryIcon() {
        return this.importResult.isSuccess ? 'standard:success' : 'standard:error';
    }

    get totalRecords() {
        return (this.importResult.successCount || 0) + (this.importResult.errorCount || 0);
    }

    get hasErrors() {
        return this.importResult.errorCount > 0;
    }

    get hasSuccessRecords() {
        return this.importResult.successCount > 0;
    }

    get errorRecords() {
        return this.importResult.processedRecords?.filter(record => !record.isSuccess) || [];
    }

    get successRecords() {
        return this.importResult.processedRecords?.filter(record => record.isSuccess) || [];
    }

    handleViewData(event) {
        const rowNumber = parseInt(event.target.dataset.row);
        const record = this.importResult.processedRecords.find(r => r.rowNumber === rowNumber);
        
        if (record && record.data) {
            this.selectedRowNumber = rowNumber;
            this.selectedRowData = Object.entries(record.data).map(([key, value]) => ({
                key,
                value: value || '(empty)'
            }));
            this.showDataModal = true;
        }
    }

    handleViewRecord(event) {
        const recordId = event.target.dataset.id;
        
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                actionName: 'view'
            }
        });
    }

    handleCloseModal() {
        this.showDataModal = false;
        this.selectedRowNumber = null;
        this.selectedRowData = [];
    }

    handleDownload() {
        this.dispatchEvent(new CustomEvent('download', {
            detail: this.importResult
        }));
    }

    handleReset() {
        this.dispatchEvent(new CustomEvent('reset'));
    }
}