.import-results .results-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.import-results .result-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.import-results .result-card.success {
    background-color: #f0fdf4;
    border-color: #86efac;
}

.import-results .result-card.error {
    background-color: #fef2f2;
    border-color: #fca5a5;
}

.import-results .result-card.total {
    background-color: #f8fafc;
    border-color: #cbd5e1;
}

.import-results .result-icon {
    flex-shrink: 0;
}

.import-results .result-details {
    flex-grow: 1;
}

.import-results .result-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.import-results .result-card.success .result-number {
    color: #16a34a;
}

.import-results .result-card.error .result-number {
    color: #dc2626;
}

.import-results .result-card.total .result-number {
    color: #475569;
}

.import-results .result-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.import-results .error-text {
    color: #dc2626;
    font-weight: 500;
}

.import-results .error-table-container {
    max-height: 400px;
    overflow-y: auto;
}

.import-results .success-records-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 0.75rem;
}

.import-results .record-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 6px;
}

.import-results .row-number {
    color: #6b7280;
    font-size: 0.875rem;
}

.import-results .data-preview-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
    align-items: start;
}

.import-results .data-field {
    display: contents;
}

.import-results .field-label {
    font-weight: 600;
    color: #374151;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.import-results .field-value {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
    word-break: break-word;
}

/* Responsive */
@media (max-width: 768px) {
    .import-results .results-grid {
        grid-template-columns: 1fr;
    }
    
    .import-results .success-records-list {
        grid-template-columns: 1fr;
    }
}