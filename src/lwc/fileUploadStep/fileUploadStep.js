import { LightningElement, api, track } from 'lwc';
import getAvailableObjects from '@salesforce/apex/ExcelImportController.getAvailableObjects';
import downloadTemplate from '@salesforce/apex/ExcelImportController.downloadTemplate';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class FileUploadStep extends LightningElement {
    @api showObjectSelection = true;
    @api allowedFileTypes = '.csv,.xlsx,.xls';

    @track objectOptions = [];
    @track selectedObject = null;
    @track uploadedFile = null;
    @track isDragOver = false;

    connectedCallback() {
        if (this.showObjectSelection) {
            this.loadObjects();
        }
    }

    async loadObjects() {
        try {
            const objects = await getAvailableObjects();
            this.objectOptions = objects.map(obj => ({
                label: obj.label,
                value: obj.apiName
            }));
        } catch (error) {
            this.showToast('Error', 'Failed to load objects: ' + error.body.message, 'error');
        }
    }

    handleObjectChange(event) {
        this.selectedObject = event.detail.value;
        this.dispatchEvent(new CustomEvent('objectselected', {
            detail: this.selectedObject
        }));
    }

    handleBrowseClick() {
        const fileInput = this.template.querySelector('.hidden-file-input');
        fileInput.click();
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    handleDrop(event) {
        event.preventDefault();
        this.isDragOver = false;
        
        const file = event.dataTransfer.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        this.isDragOver = true;
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.isDragOver = false;
    }

    async processFile(file) {
        // Validate file type
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const allowedTypes = this.allowedFileTypes.split(',').map(type => type.replace('.', ''));
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showToast('Error', 'Invalid file type. Please upload ' + this.allowedFileTypes, 'error');
            return;
        }

        // Validate file size (10MB limit)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showToast('Error', 'File size exceeds 10MB limit', 'error');
            return;
        }

        this.uploadedFile = file;
        
        // Convert file to base64
        const base64Data = await this.fileToBase64(file);
        
        // Dispatch upload complete event
        this.dispatchEvent(new CustomEvent('uploadcomplete', {
            detail: {
                fileName: file.name,
                base64Data: base64Data,
                size: file.size
            }
        }));
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    handleRemoveFile() {
        this.uploadedFile = null;
        const fileInput = this.template.querySelector('.hidden-file-input');
        fileInput.value = '';
    }

    async handleDownloadTemplate() {
        try {
            // Get object fields (for simplicity, using common fields)
            const fields = ['Name', 'Email', 'Phone', 'Industry'];
            const templateData = await downloadTemplate({ 
                objectName: this.selectedObject,
                fields: fields
            });
            
            // Create and download file
            const blob = this.base64ToBlob(templateData, 'text/csv');
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.selectedObject}_template.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.showToast('Success', 'Template downloaded successfully', 'success');
        } catch (error) {
            this.showToast('Error', 'Failed to download template: ' + error.body.message, 'error');
        }
    }

    base64ToBlob(base64Data, contentType) {
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: contentType });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title,
            message,
            variant
        }));
    }
}