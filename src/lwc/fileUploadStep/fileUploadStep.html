<template>
    <div class="file-upload-step">
        <!-- Object Selection -->
        <div if:true={showObjectSelection} class="slds-m-bottom_medium">
            <lightning-combobox
                name="objectSelect"
                label="Select Object"
                placeholder="Choose an object to import data into"
                options={objectOptions}
                onchange={handleObjectChange}
                required>
            </lightning-combobox>
        </div>

        <!-- File Upload Area -->
        <div class="upload-area">
            <div class="upload-zone" ondrop={handleDrop} ondragover={handleDragOver} ondragleave={handleDragLeave}>
                <div class="upload-content">
                    <lightning-icon icon-name="standard:spreadsheet" size="large" class="slds-m-bottom_small"></lightning-icon>
                    <h3 class="slds-text-heading_medium slds-m-bottom_small">Drop your Excel file here</h3>
                    <p class="slds-text-body_regular slds-m-bottom_medium">or</p>
                    <lightning-button
                        label="Browse Files"
                        variant="brand"
                        onclick={handleBrowseClick}>
                    </lightning-button>
                    <input type="file" class="hidden-file-input" accept={allowedFileTypes} onchange={handleFileSelect}>
                    <p class="slds-text-body_small slds-m-top_medium">
                        Supported formats: {allowedFileTypes}
                    </p>
                </div>
            </div>
        </div>

        <!-- File Info -->
        <div if:true={uploadedFile} class="slds-m-top_medium file-info">
            <lightning-card title="Uploaded File" icon-name="standard:file">
                <div class="slds-p-around_medium">
                    <div class="file-details">
                        <lightning-icon icon-name="standard:file" size="small"></lightning-icon>
                        <div class="file-name">{uploadedFile.name}</div>
                        <div class="file-size">{formatFileSize(uploadedFile.size)}</div>
                    </div>
                    <div class="slds-m-top_small">
                        <lightning-button
                            label="Remove File"
                            variant="destructive"
                            onclick={handleRemoveFile}>
                        </lightning-button>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Template Download -->
        <div class="slds-m-top_large">
            <lightning-button
                label="Download Template"
                variant="outline-brand"
                onclick={handleDownloadTemplate}
                disabled={!selectedObject}>
            </lightning-button>
        </div>
    </div>
</template>