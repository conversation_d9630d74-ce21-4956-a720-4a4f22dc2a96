import { LightningElement, api, track } from 'lwc';
import importExcelData from '@salesforce/apex/ExcelImportController.importExcelData';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class DataPreview extends LightningElement {
    @api fileData;
    @api previewData = [];
    @api fieldMappings = {};
    @api selectedObject;

    @track validationOnly = false;
    @track batchSize = 50;
    @track mappingDisplay = [];
    @track previewColumns = [];
    @track displayData = [];

    connectedCallback() {
        this.preparePreview();
    }

    preparePreview() {
        // Prepare mapping display
        this.mappingDisplay = Object.entries(this.fieldMappings).map(([excelColumn, salesforceField]) => ({
            excelColumn,
            salesforceField
        }));

        // Prepare table columns
        this.previewColumns = [
            { label: 'Row #', key: 'rowNumber' },
            ...this.mappingDisplay.map(mapping => ({
                label: mapping.excelColumn,
                key: mapping.excelColumn
            }))
        ];

        // Prepare display data
        this.displayData = this.previewData.slice(0, 10).map((row, index) => ({
            rowNumber: index + 1,
            ...row
        }));
    }

    async handleImport() {
        // Show loading state
        this.isLoading = true;
        
        try {
            const result = await importExcelData({
                base64Data: this.fileData.base64Data,
                fileName: this.fileData.fileName,
                objectName: this.selectedObject,
                fieldMappings: this.fieldMappings
            });

            this.dispatchEvent(new CustomEvent('import', {
                detail: result
            }));

            if (result.isSuccess) {
                this.showToast(
                    'Import Complete', 
                    `Successfully imported ${result.successCount} records. ${result.errorCount} errors.`,
                    result.errorCount > 0 ? 'warning' : 'success'
                );
            } else {
                this.showToast('Import Failed', result.errorMessage, 'error');
            }
        } catch (error) {
            this.showToast('Error', 'Import failed: ' + error.body.message, 'error');
        } finally {
            this.isLoading = false;
        }
    }

    handleValidationToggle(event) {
        this.validationOnly = event.target.checked;
    }

    handleBatchSizeChange(event) {
        this.batchSize = parseInt(event.target.value);
    }

    handleBack() {
        this.dispatchEvent(new CustomEvent('back'));
    }

    // Getters
    get totalRecords() {
        return this.previewData.length;
    }

    get mappedFieldsCount() {
        return Object.keys(this.fieldMappings).length;
    }

    get hasMoreRecords() {
        return this.previewData.length > 10;
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title,
            message,
            variant
        }));
    }
}