<template>
    <div class="data-preview">
        <!-- Summary Card -->
        <div class="slds-m-bottom_large">
            <lightning-card title="Import Summary" icon-name="standard:summary">
                <div class="slds-p-around_medium">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">Total Records</span>
                            <span class="summary-value">{totalRecords}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Mapped Fields</span>
                            <span class="summary-value">{mappedFieldsCount}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Target Object</span>
                            <span class="summary-value">{selectedObject}</span>
                        </div>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Field Mapping Display -->
        <div class="slds-m-bottom_large">
            <lightning-card title="Field Mappings" icon-name="standard:mapping_rule">
                <div class="slds-p-around_medium">
                    <div class="mappings-grid">
                        <template for:each={mappingDisplay} for:item="mapping">
                            <div key={mapping.excelColumn} class="mapping-item">
                                <span class="excel-field">{mapping.excelColumn}</span>
                                <lightning-icon icon-name="utility:arrow_right" size="x-small"></lightning-icon>
                                <span class="salesforce-field">{mapping.salesforceField}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Data Preview Table -->
        <div class="slds-m-bottom_large">
            <lightning-card title="Data Preview (First 10 rows)" icon-name="standard:preview">
                <div class="slds-p-around_medium">
                    <div class="table-container">
                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <template for:each={previewColumns} for:item="column">
                                        <th key={column.key} scope="col">
                                            <div class="slds-truncate" title={column.label}>{column.label}</div>
                                        </th>
                                    </template>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={displayData} for:item="row">
                                    <tr key={row.rowNumber} class="slds-hint-parent">
                                        <template for:each={previewColumns} for:item="column">
                                            <td key={column.key} data-label={column.label}>
                                                <div class="slds-truncate" title={row[column.key]}>
                                                    {row[column.key]}
                                                </div>
                                            </td>
                                        </template>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div if:true={hasMoreRecords} class="slds-m-top_small">
                        <p class="slds-text-body_small">
                            Showing first 10 of {totalRecords} records
                        </p>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Import Options -->
        <div class="slds-m-bottom_large">
            <lightning-card title="Import Options" icon-name="standard:settings">
                <div class="slds-p-around_medium">
                    <lightning-input
                        type="checkbox"
                        label="Run validation only (don't create records)"
                        onchange={handleValidationToggle}>
                    </lightning-input>
                    <div class="slds-m-top_small">
                        <lightning-input
                            type="number"
                            label="Batch size"
                            value={batchSize}
                            min="1"
                            max="200"
                            onchange={handleBatchSizeChange}>
                        </lightning-input>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Action Buttons -->
        <div class="slds-m-top_large">
            <lightning-button
                label="Start Import"
                variant="brand"
                onclick={handleImport}
                class="slds-m-right_small">
            </lightning-button>
            <lightning-button
                label="Back"
                variant="neutral"
                onclick={handleBack}>
            </lightning-button>
        </div>
    </div>
</template>