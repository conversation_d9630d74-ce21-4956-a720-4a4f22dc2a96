.data-preview .summary-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.data-preview .summary-item {
    text-align: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 8px;
}

.data-preview .summary-label {
    display: block;
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.data-preview .summary-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.data-preview .mappings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 0.75rem;
}

.data-preview .mapping-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: #f9fafb;
    border-radius: 6px;
}

.data-preview .excel-field {
    font-weight: 500;
    color: #6b7280;
}

.data-preview .salesforce-field {
    font-weight: 600;
    color: #1f2937;
}

.data-preview .table-container {
    overflow-x: auto;
}

.data-preview .slds-table th {
    background-color: #f9fafb;
    font-weight: 600;
}

.data-preview .slds-table td {
    vertical-align: top;
}

/* Responsive */
@media (max-width: 768px) {
    .data-preview .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .data-preview .mappings-grid {
        grid-template-columns: 1fr;
    }
}