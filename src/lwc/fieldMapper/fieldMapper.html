<template>
    <div class="field-mapper">
        <!-- Excel Columns Section -->
        <div class="slds-m-bottom_large">
            <h2 class="slds-text-heading_medium slds-m-bottom_medium">Excel Columns</h2>
            <div class="columns-grid">
                <div class="excel-column">
                    <div class="column-header">Excel Column</div>
                    <div class="column-body">
                        <template for:each={excelColumns} for:item="column">
                            <div key={column} class="column-item">
                                <lightning-icon icon-name="standard:columns" size="x-small"></lightning-icon>
                                <span>{column}</span>
                            </div>
                        </template>
                    </div>
                </div>
                
                <div class="mapping-arrow">
                    <lightning-icon icon-name="utility:arrow_right" size="small"></lightning-icon>
                </div>
                
                <div class="salesforce-field">
                    <div class="column-header">Salesforce Field</div>
                    <div class="column-body">
                        <template for:each={excelColumns} for:item="column">
                            <div key={column} class="field-mapping-item">
                                <lightning-combobox
                                    name={column}
                                    placeholder="Select field"
                                    options={fieldOptions}
                                    onchange={handleFieldChange}
                                    data-column={column}
                                    value={mappings[column]}>
                                </lightning-combobox>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-mapping Section -->
        <div class="slds-m-bottom_large">
            <lightning-button
                label="Auto-Map Fields"
                variant="brand"
                onclick={handleAutoMap}
                class="slds-m-right_small">
            </lightning-button>
            <lightning-button
                label="Clear Mappings"
                variant="neutral"
                onclick={handleClearMappings}>
            </lightning-button>
        </div>

        <!-- Mapping Summary -->
        <div class="mapping-summary">
            <lightning-card title="Mapping Summary" icon-name="standard:mapping_rule">
                <div class="slds-p-around_medium">
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Columns:</span>
                            <span class="stat-value">{totalColumns}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Mapped:</span>
                            <span class="stat-value success">{mappedCount}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Unmapped:</span>
                            <span class="stat-value warning">{unmappedCount}</span>
                        </div>
                    </div>
                </div>
            </lightning-card>
        </div>

        <!-- Action Buttons -->
        <div class="slds-m-top_large">
            <lightning-button
                label="Continue"
                variant="brand"
                onclick={handleContinue}
                disabled={mappedCount === 0}
                class="slds-m-right_small">
            </lightning-button>
            <lightning-button
                label="Back"
                variant="neutral"
                onclick={handleBack}>
            </lightning-button>
        </div>
    </div>
</template>