.field-mapper .columns-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: start;
}

.field-mapper .excel-column,
.field-mapper .salesforce-field {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
}

.field-mapper .column-header {
    background-color: #f3f3f3;
    padding: 0.75rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
}

.field-mapper .column-body {
    max-height: 400px;
    overflow-y: auto;
}

.field-mapper .column-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
}

.field-mapper .column-item:last-child {
    border-bottom: none;
}

.field-mapper .field-mapping-item {
    padding: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.field-mapper .field-mapping-item:last-child {
    border-bottom: none;
}

.field-mapper .mapping-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 2rem;
}

.field-mapper .summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.field-mapper .stat-item {
    text-align: center;
}

.field-mapper .stat-label {
    display: block;
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.field-mapper .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
}

.field-mapper .stat-value.success {
    color: #10b981;
}

.field-mapper .stat-value.warning {
    color: #f59e0b;
}

/* Responsive */
@media (max-width: 768px) {
    .field-mapper .columns-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .field-mapper .mapping-arrow {
        transform: rotate(90deg);
    }
    
    .field-mapper .summary-stats {
        grid-template-columns: 1fr;
    }
}