import { LightningElement, api, track } from 'lwc';
import getObjectFields from '@salesforce/apex/ExcelImportController.getObjectFields';
import previewExcelData from '@salesforce/apex/ExcelImportController.previewExcelData';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class FieldMapper extends LightningElement {
    @api fileData;
    @api selectedObject;

    @track excelColumns = [];
    @track fieldOptions = [];
    @track mappings = {};
    @track previewData = [];

    connectedCallback() {
        this.loadFields();
        this.previewData = [];
    }

    async loadFields() {
        try {
            const fields = await getObjectFields({ objectName: this.selectedObject });
            this.fieldOptions = fields.map(field => ({
                label: `${field.label} (${field.apiName})`,
                value: field.apiName,
                type: field.type,
                required: field.isRequired
            }));

            // Get Excel columns from file data
            this.previewData = await previewExcelData({
                base64Data: this.fileData.base64Data,
                fileName: this.fileData.fileName,
                maxRows: 5
            });

            if (this.previewData.length > 0) {
                this.excelColumns = Object.keys(this.previewData[0]);
                this.autoMapFields();
            }
        } catch (error) {
            this.showToast('Error', 'Failed to load fields: ' + error.body.message, 'error');
        }
    }

    autoMapFields() {
        this.excelColumns.forEach(excelColumn => {
            const normalizedExcel = excelColumn.toLowerCase().replace(/[^a-z0-9]/g, '');
            
            // Try to find a matching field
            const matchedField = this.fieldOptions.find(field => {
                const normalizedField = field.value.toLowerCase().replace(/[^a-z0-9]/g, '');
                return normalizedField === normalizedExcel || 
                       normalizedField.includes(normalizedExcel) ||
                       normalizedExcel.includes(normalizedField);
            });

            if (matchedField) {
                this.mappings[excelColumn] = matchedField.value;
            }
        });
    }

    handleFieldChange(event) {
        const column = event.target.dataset.column;
        const value = event.detail.value;
        
        if (value) {
            this.mappings[column] = value;
        } else {
            delete this.mappings[column];
        }
    }

    handleAutoMap() {
        this.mappings = {};
        this.autoMapFields();
        this.showToast('Success', 'Fields auto-mapped based on column names', 'success');
    }

    handleClearMappings() {
        this.mappings = {};
        this.showToast('Success', 'All mappings cleared', 'success');
    }

    handleContinue() {
        if (Object.keys(this.mappings).length === 0) {
            this.showToast('Error', 'Please map at least one field', 'error');
            return;
        }

        // Get full preview data
        previewExcelData({
            base64Data: this.fileData.base64Data,
            fileName: this.fileData.fileName,
            maxRows: 1000 // Get more rows for preview
        }).then(data => {
            this.dispatchEvent(new CustomEvent('mappingcomplete', {
                detail: {
                    fileData: this.fileData,
                    mappings: this.mappings,
                    previewData: data
                }
            }));
        }).catch(error => {
            this.showToast('Error', 'Failed to load preview data: ' + error.body.message, 'error');
        });
    }

    handleBack() {
        this.dispatchEvent(new CustomEvent('back'));
    }

    // Getters
    get totalColumns() {
        return this.excelColumns.length;
    }

    get mappedCount() {
        return Object.keys(this.mappings).length;
    }

    get unmappedCount() {
        return this.totalColumns - this.mappedCount;
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title,
            message,
            variant
        }));
    }
}