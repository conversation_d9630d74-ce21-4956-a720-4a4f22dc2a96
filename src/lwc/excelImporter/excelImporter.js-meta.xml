<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>Excel Importer</masterLabel>
    <description>Import Excel data into Salesforce objects with dynamic field mapping</description>
    <targets>
        <target>lightning__AppPage</target>
        <target>lightning__RecordPage</target>
        <target>lightning__HomePage</target>
        <target>lightning__Tab</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__AppPage,lightning__RecordPage,lightning__HomePage">
            <property name="title" type="String" default="Excel Data Importer" label="Component Title"/>
            <property name="showObjectSelection" type="Boolean" default="true" label="Show Object Selection"/>
            <property name="allowedFileTypes" type="String" default=".csv,.xlsx,.xls" label="Allowed File Types"/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>