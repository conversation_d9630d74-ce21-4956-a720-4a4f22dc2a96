import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';

export default class ExcelImporter extends NavigationMixin(LightningElement) {
    @api title = 'Excel Data Importer';
    @api showObjectSelection = true;
    @api allowedFileTypes = '.csv,.xlsx,.xls';

    @track currentStep = '1';
    @track fileData = null;
    @track selectedObject = null;
    @track fieldMappings = {};
    @track previewData = [];
    @track importResult = null;

    // Getters for step visibility
    get isStep1() {
        return this.currentStep === '1';
    }

    get isStep2() {
        return this.currentStep === '2';
    }

    get isStep3() {
        return this.currentStep === '3';
    }

    get isStep4() {
        return this.currentStep === '4';
    }

    // Event handlers
    handleFileUpload(event) {
        this.fileData = event.detail;
        this.showToast('Success', 'File uploaded successfully', 'success');
        
        if (this.selectedObject) {
            this.currentStep = '2';
        }
    }

    handleObjectSelection(event) {
        this.selectedObject = event.detail;
        
        if (this.fileData) {
            this.currentStep = '2';
        }
    }

    handleMappingComplete(event) {
        this.fileData = event.detail.fileData || this.fileData;
        this.fieldMappings = event.detail.mappings;
        this.previewData = event.detail.previewData;
        this.currentStep = '3';
    }

    handleImport(event) {
        this.importResult = event.detail;
        this.currentStep = '4';
    }

    handleBack() {
        const stepNumber = parseInt(this.currentStep);
        if (stepNumber > 1) {
            this.currentStep = String(stepNumber - 1);
        }
    }

    handleReset() {
        this.currentStep = '1';
        this.fileData = null;
        this.selectedObject = null;
        this.fieldMappings = {};
        this.previewData = [];
        this.importResult = null;
    }

    handleDownloadResults(event) {
        const results = event.detail;
        
        // Create CSV content
        let csvContent = 'Status,Record ID,Row Number,Error Message\n';
        
        results.processedRecords.forEach(record => {
            const status = record.isSuccess ? 'Success' : 'Error';
            const recordId = record.recordId || '';
            const rowNum = record.rowNumber || '';
            const errorMsg = record.errorMessage || '';
            
            csvContent += `${status},${recordId},${rowNum},"${errorMsg}"\n`;
        });
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `import_results_${Date.now()}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        this.showToast('Success', 'Results downloaded successfully', 'success');
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title,
            message,
            variant
        }));
    }
}