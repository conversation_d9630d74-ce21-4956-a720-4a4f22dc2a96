import { createElement } from 'lwc';
import ExcelImporter from 'c/excelImporter';
import { ShowToastEventName } from 'lightning/platformShowToastEvent';

// Mock the child components
jest.mock('c/fileUploadStep', () => {
    return {
        __esModule: true,
        default: jest.fn()
    };
});

jest.mock('c/fieldMapper', () => {
    return {
        __esModule: true,
        default: jest.fn()
    };
});

jest.mock('c/dataPreview', () => {
    return {
        __esModule: true,
        default: jest.fn()
    };
});

jest.mock('c/importResults', () => {
    return {
        __esModule: true,
        default: jest.fn()
    };
});

describe('c-excel-importer', () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it('renders the component with default title', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        return Promise.resolve().then(() => {
            const card = element.shadowRoot.querySelector('lightning-card');
            expect(card.title).toBe('Excel Data Importer');
        });
    });

    it('shows step 1 by default', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        return Promise.resolve().then(() => {
            const step1 = element.shadowRoot.querySelector('[data-step="1"]');
            expect(step1).toBeTruthy();
        });
    });

    it('moves to step 2 when both file and object are selected', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        // Simulate file upload
        const mockFileData = {
            fileName: 'test.csv',
            base64Data: 'dGVzdC1kYXRh',
            size: 1024
        };
        element.handleFileUpload({ detail: mockFileData });

        // Simulate object selection
        element.handleObjectSelection({ detail: 'Account' });

        return Promise.resolve().then(() => {
            expect(element.currentStep).toBe('2');
        });
    });

    it('shows toast message on file upload', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        const handler = jest.fn();
        element.addEventListener(ShowToastEventName, handler);

        const mockFileData = {
            fileName: 'test.csv',
            base64Data: 'dGVzdC1kYXRh',
            size: 1024
        };
        element.handleFileUpload({ detail: mockFileData });

        expect(handler).toHaveBeenCalled();
        const eventData = handler.mock.calls[0][0].detail;
        expect(eventData.title).toBe('Success');
        expect(eventData.message).toBe('File uploaded successfully');
        expect(eventData.variant).toBe('success');
    });

    it('resets all data when handleReset is called', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        // Set some data
        element.currentStep = '4';
        element.fileData = { fileName: 'test.csv' };
        element.selectedObject = 'Account';
        element.fieldMappings = { Name: 'Name' };
        element.previewData = [{ Name: 'Test' }];
        element.importResult = { successCount: 1 };

        element.handleReset();

        expect(element.currentStep).toBe('1');
        expect(element.fileData).toBeNull();
        expect(element.selectedObject).toBeNull();
        expect(element.fieldMappings).toEqual({});
        expect(element.previewData).toEqual([]);
        expect(element.importResult).toBeNull();
    });

    it('downloads results as CSV file', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        // Mock URL.createObjectURL and revokeObjectURL
        global.URL.createObjectURL = jest.fn(() => 'blob:test');
        global.URL.revokeObjectURL = jest.fn();
        
        // Mock document methods
        const mockAnchor = {
            href: '',
            download: '',
            click: jest.fn()
        };
        document.createElement = jest.fn().mockReturnValue(mockAnchor);
        document.body.appendChild = jest.fn();
        document.body.removeChild = jest.fn();

        const mockResults = {
            processedRecords: [
                {
                    isSuccess: true,
                    recordId: '001ABC123',
                    rowNumber: 1,
                    errorMessage: ''
                },
                {
                    isSuccess: false,
                    recordId: '',
                    rowNumber: 2,
                    errorMessage: 'Required field missing'
                }
            ]
        };

        element.handleDownloadResults({ detail: mockResults });

        expect(document.createElement).toHaveBeenCalledWith('a');
        expect(mockAnchor.download).toMatch(/import_results_\d+\.csv/);
        expect(mockAnchor.click).toHaveBeenCalled();
    });

    it('goes back to previous step when handleBack is called', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        element.currentStep = '3';
        element.handleBack();

        expect(element.currentStep).toBe('2');
    });

    it('does not go back from step 1', () => {
        const element = createElement('c-excel-importer', {
            is: ExcelImporter
        });
        document.body.appendChild(element);

        element.currentStep = '1';
        element.handleBack();

        expect(element.currentStep).toBe('1');
    });
});