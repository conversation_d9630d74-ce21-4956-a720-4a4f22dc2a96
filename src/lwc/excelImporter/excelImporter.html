<template>
    <div class="excel-importer">
        <lightning-card title={title} icon-name="standard:spreadsheet">
            <div class="slds-p-around_medium">
                <!-- Progress Indicator -->
                <div class="slds-m-bottom_medium">
                    <lightning-progress-indicator
                        current-step={currentStep}
                        type="base"
                        variant="base">
                        <lightning-progress-step label="Upload" value="1"></lightning-progress-step>
                        <lightning-progress-step label="Map Fields" value="2"></lightning-progress-step>
                        <lightning-progress-step label="Preview" value="3"></lightlightning-progress-step>
                        <lightning-progress-step label="Import" value="4"></lightning-progress-step>
                    </lightning-progress-indicator>
                </div>

                <!-- Step 1: File Upload -->
                <div if:true={isStep1}>
                    <c-file-upload-step
                        onuploadcomplete={handleFileUpload}
                        onobjectselected={handleObjectSelection}
                        show-object-selection={showObjectSelection}
                        allowed-file-types={allowedFileTypes}>
                    </c-file-upload-step>
                </div>

                <!-- Step 2: Field Mapping -->
                <div if:true={isStep2}>
                    <c-field-mapper
                        file-data={fileData}
                        selected-object={selectedObject}
                        onmappingcomplete={handleMappingComplete}
                        onback={handleBack}>
                    </c-field-mapper>
                </div>

                <!-- Step 3: Data Preview -->
                <div if:true={isStep3}>
                    <c-data-preview
                        file-data={fileData}
                        preview-data={previewData}
                        field-mappings={fieldMappings}
                        selected-object={selectedObject}
                        onimport={handleImport}
                        onback={handleBack}>
                    </c-data-preview>
                </div>

                <!-- Step 4: Import Results -->
                <div if:true={isStep4}>
                    <c-import-results
                        import-result={importResult}
                        onreset={handleReset}
                        ondownload={handleDownloadResults}>
                    </c-import-results>
                </div>
            </div>
        </lightning-card>
    </div>
</template>