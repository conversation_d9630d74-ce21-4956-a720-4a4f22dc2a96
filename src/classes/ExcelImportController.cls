public class ExcelImportController {

	@AuraEnabled
	public static ImportResult importExcelData(String base64Data, String fileName, String objectName, Map<String, String> fieldMappings) {
		ImportResult result = new ImportResult();

		try {
			// Convert base64 to blob
			Blob fileData = EncodingUtil.base64Decode(base64Data);

			// Parse Excel/CSV data
			List<Map<String, String>> excelData = parseExcelFile(fileData, fileName);

			if (excelData.isEmpty()) {
				result.isSuccess = false;
				result.errorMessage = 'No data found in the file';
				return result;
			}

			if (fieldMappings == null || fieldMappings.isEmpty()) {
				result.isSuccess = false;
				result.errorMessage = 'No field mappings provided';
				return result;
			}

			// Get object metadata
			Map<String, Schema.SObjectField> fieldMap = getObjectFieldsInternal(objectName);

			// Process records in batches
			result.processedRecords = new List<ProcessedRecord>();
			result.successCount = 0;
			result.errorCount = 0;

			// Process in batches of 200 to avoid governor limits
			Integer batchSize = 200;
			for (Integer i = 0; i < excelData.size(); i += batchSize) {
				Integer endIndex = Math.min(i + batchSize, excelData.size());
				List<Map<String, String>> batch = excelData.add(i, endIndex);

				// Process each record in the batch
				for (Integer j = 0; j < batch.size(); j++) {
					Map<String, String> rowData = batch[j];
					ProcessedRecord processed = processRecord(rowData, objectName, fieldMappings, fieldMap, i + j + 1);
					result.processedRecords.add(processed);

					if (processed.isSuccess) {
						result.successCount++;
					} else {
						result.errorCount++;
					}
				}
			}

			result.isSuccess = true;

		} catch (Exception e) {
			result.isSuccess = false;
			result.errorMessage = 'Error processing file: ' + e.getMessage();
		}

		return result;
	}

	@AuraEnabled
	public static List<ObjectInfo> getAvailableObjects() {
		List<ObjectInfo> objects = new List<ObjectInfo>();

		for (Schema.SObjectType objType : Schema.getGlobalDescribe().values()) {
			Schema.DescribeSObjectResult objDescribe = objType.getDescribe();

			if (objDescribe.isCreateable() && objDescribe.isAccessible()) {
				ObjectInfo info = new ObjectInfo();
				info.apiName = objDescribe.getName();
				info.label = objDescribe.getLabel();
				objects.add(info);
			}
		}

		return objects;
	}

	@AuraEnabled
	public static List<FieldInfo> getObjectFields(String objectName) {
		List<FieldInfo> fields = new List<FieldInfo>();

		if (String.isBlank(objectName)) {
			return fields;
		}

		Schema.SObjectType objType = Schema.getGlobalDescribe().get(objectName);
		if (objType == null) {
			return fields;
		}

		Map<String, Schema.SObjectField> fieldMap = objType.getDescribe().fields.getMap();

		for (String fieldName : fieldMap.keySet()) {
			Schema.SObjectField field = fieldMap.get(fieldName);
			Schema.DescribeFieldResult fieldDescribe = field.getDescribe();

			if (fieldDescribe.isCreateable() && fieldDescribe.isAccessible()) {
				FieldInfo info = new FieldInfo();
				info.apiName = fieldName;
				info.label = fieldDescribe.getLabel();
				info.type = String.valueOf(fieldDescribe.getType());
				info.isRequired = fieldDescribe.isNillable() == false;
				info.isUpdateable = fieldDescribe.isUpdateable();
				fields.add(info);
			}
		}

		return fields;
	}

	@AuraEnabled
	public static List<Map<String, String>> previewExcelData(String base64Data, String fileName, Integer maxRows) {
		Blob fileData = EncodingUtil.base64Decode(base64Data);
		List<Map<String, String>> previewData = parseExcelFile(fileData, fileName);

		if (previewData.size() > maxRows) {
			previewData = previewData.subList(0, maxRows);
		}

		return previewData;
	}

	@AuraEnabled
	public static String downloadTemplate(String objectName, List<String> fields) {
		// Generate CSV template
		String csvContent = '';

		// Add header row
		csvContent = String.join(fields, ',') + '\n';

		// Add sample data row
		List<String> sampleRow = new List<String>();
		for (String field : fields) {
			sampleRow.add('Sample_' + field);
		}
		csvContent += String.join(sampleRow, ',');

		return EncodingUtil.base64Encode(Blob.valueOf(csvContent));
	}

	private static List<Map<String, String>> parseExcelFile(Blob fileData, String fileName) {
		List<Map<String, String>> result = new List<Map<String, String>>();

		if (fileName.endsWith('.csv')) {
			result = parseCSV(fileData.toString());
		} else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
			// For Excel files, we'll use a simple CSV conversion approach
			// In a real implementation, you might use a library like Apache POI
			result = parseExcelAsCSV(fileData);
		}

		return result;
	}

	private static List<Map<String, String>> parseCSV(String csvContent) {
		List<Map<String, String>> result = new List<Map<String, String>>();

		// Handle different line endings
		String normalizedContent = csvContent.replace('\r\n', '\n').replace('\r', '\n');
		List<String> lines = normalizedContent.split('\n');

		if (lines.isEmpty()) return result;

		// Remove empty lines
		List<String> nonEmptyLines = new List<String>();
		for (String line : lines) {
			if (String.isNotBlank(line)) {
				nonEmptyLines.add(line);
			}
		}

		if (nonEmptyLines.isEmpty()) return result;

		// Get headers - handle quoted values
		String[] headers = parseCSVLine(nonEmptyLines[0]);

		// Process data rows
		for (Integer i = 1; i < nonEmptyLines.size(); i++) {
			String[] values = parseCSVLine(nonEmptyLines[i]);
			Map<String, String> row = new Map<String, String>();

			for (Integer j = 0; j < Math.min(headers.size(), values.size()); j++) {
				String header = headers[j].trim();
				String value = values[j].trim();
				// Remove surrounding quotes if present
				if (value.startsWith('"') && value.endsWith('"')) {
					value = value.substring(1, value.length() - 1);
				}
				row.put(header, value);
			}

			result.add(row);
		}

		return result;
	}

	private static String[] parseCSVLine(String line) {
		List<String> values = new List<String>();
		Boolean inQuotes = false;
		String currentValue = '';

		for (Integer i = 0; i < line.length(); i++) {
			String c = line.substring(i, i + 1);

			if (c == '"') {
				if (inQuotes && i < line.length() - 1 && line.substring(i + 1, i + 2) == '"') {
					// Escaped quote
					currentValue += '"';
					i++;
				} else {
					inQuotes = !inQuotes;
				}
			} else if (c == ',' && !inQuotes) {
				values.add(currentValue);
				currentValue = '';
			} else {
				currentValue += c;
			}
		}

		values.add(currentValue);
		return values;
	}

	private static List<Map<String, String>> parseExcelAsCSV(Blob excelData) {
		// Simplified Excel parsing - in production, use a proper library
		// This is a placeholder implementation
		return new List<Map<String, String>>();
	}

	private static Map<String, Schema.SObjectField> getObjectFieldsInternal(String objectName) {
		Schema.SObjectType objType = Schema.getGlobalDescribe().get(objectName);
		if (objType == null) {
			return new Map<String, Schema.SObjectField>();
		}
		return objType.getDescribe().fields.getMap();
	}

	private static ProcessedRecord processRecord(Map<String, String> rowData, String objectName,
			Map<String, String> fieldMappings,
			Map<String, Schema.SObjectField> fieldMap,
			Integer rowNum) {
		ProcessedRecord processed = new ProcessedRecord();
		processed.rowNumber = rowNum;
		processed.data = rowData;

		try {
			// Check if object exists
			Schema.SObjectType objType = Schema.getGlobalDescribe().get(objectName);
			if (objType == null) {
				throw new Exception('Object ' + objectName + ' does not exist');
			}

			// Create SObject dynamically
			SObject record = objType.newSObject();

			// Map fields
			for (String excelColumn : fieldMappings.keySet()) {
				String salesforceField = fieldMappings.get(excelColumn);
				String value = rowData.get(excelColumn);

				if (String.isNotBlank(value) && fieldMap.containsKey(salesforceField)) {
					Schema.SObjectField field = fieldMap.get(salesforceField);
					Schema.DescribeFieldResult fieldDescribe = field.getDescribe();

					// Check if field is createable
					if (!fieldDescribe.isCreateable()) {
						continue;
					}

					// Convert value based on field type
					Object convertedValue = convertValue(value, fieldDescribe.getType());

					// Don't set null values for required fields
					if (convertedValue != null || !fieldDescribe.isNillable()) {
						record.put(salesforceField, convertedValue);
					}
				}
			}

			// Insert record
			insert record;

			processed.isSuccess = true;
			processed.recordId = record.Id;

		} catch (Exception e) {
			processed.isSuccess = false;
			processed.errorMessage = 'Row ' + rowNum + ': ' + e.getMessage();
		}

		return processed;
	}

	private static Object convertValue(String value, Schema.DisplayType fieldType) {
		if (String.isBlank(value)) return null;

		try {
			switch on fieldType {
				when STRING, ID, TEXTAREA, EMAIL, URL, PHONE {
					return value;
				}
				when INTEGER {
					return Integer.valueOf(value);
				}
				when DOUBLE, CURRENCY, PERCENT {
					return Decimal.valueOf(value);
				}
				when BOOLEAN {
					String lowerValue = value.toLowerCase();
					if (lowerValue == 'true' || lowerValue == 'yes' || lowerValue == '1') {
						return true;
					} else if (lowerValue == 'false' || lowerValue == 'no' || lowerValue == '0') {
						return false;
					}
					return Boolean.valueOf(value);
				}
				when DATE {
					// Try different date formats
					if (value.contains('/')) {
						return Date.parse(value);
					} else if (value.contains('-')) {
						return Date.valueOf(value);
					}
					// Try to parse as MM/DD/YYYY
					String[] parts = value.split('/');
					if (parts.size() == 3) {
						return Date.newInstance(
								Integer.valueOf(parts[2]),
								Integer.valueOf(parts[0]),
								Integer.valueOf(parts[1])
						);
					}
					return Date.parse(value);
				}
				when DATETIME {
					return DateTime.parse(value);
				}
				when else {
					return value;
				}
			}
		} catch (Exception e) {
			// Return original value if conversion fails
			return value;
		}
	}

	public class ImportResult {
		@AuraEnabled public Boolean isSuccess { get; set; }
		@AuraEnabled public String errorMessage { get; set; }
		@AuraEnabled public List<ProcessedRecord> processedRecords { get; set; }
		@AuraEnabled public Integer successCount { get; set; }
		@AuraEnabled public Integer errorCount { get; set; }
	}

	public class ProcessedRecord {
		@AuraEnabled public Integer rowNumber { get; set; }
		@AuraEnabled public Boolean isSuccess { get; set; }
		@AuraEnabled public String recordId { get; set; }
		@AuraEnabled public String errorMessage { get; set; }
		@AuraEnabled public Map<String, String> data { get; set; }
	}

	public class ObjectInfo {
		@AuraEnabled public String apiName { get; set; }
		@AuraEnabled public String label { get; set; }
	}

	public class FieldInfo {
		@AuraEnabled public String apiName { get; set; }
		@AuraEnabled public String label { get; set; }
		@AuraEnabled public String type { get; set; }
		@AuraEnabled public Boolean isRequired { get; set; }
		@AuraEnabled public Boolean isUpdateable { get; set; }
	}
}