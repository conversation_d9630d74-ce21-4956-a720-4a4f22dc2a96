@IsTest
public class ExcelImportControllerTests {
    
    @TestSetup
    static void setup() {
        // Create test data
        Account testAccount = new Account(
            Name = 'Test Account',
            Industry = 'Technology',
            Phone = '************'
        );
        insert testAccount;
    }
    
    @IsTest
    static void testGetAvailableObjects() {
        Test.startTest();
        List<ExcelImportController.ObjectInfo> objects = ExcelImportController.getAvailableObjects();
        Test.stopTest();
        
        // Should return at least Account object
        System.assert(objects.size() > 0, 'Should return at least one object');
        
        Boolean hasAccount = false;
        for (ExcelImportController.ObjectInfo obj : objects) {
            if (obj.apiName == 'Account') {
                hasAccount = true;
                break;
            }
        }
        System.assert(hasAccount, 'Should include Account object');
    }
    
    @IsTest
    static void testGetObjectFields() {
        Test.startTest();
        List<ExcelImportController.FieldInfo> fields = ExcelImportController.getObjectFields('Account');
        Test.stopTest();
        
        // Should return Account fields
        System.assert(fields.size() > 0, 'Should return Account fields');
        
        // Check for specific fields
        Boolean hasName = false;
        Boolean hasIndustry = false;
        
        for (ExcelImportController.FieldInfo field : fields) {
            if (field.apiName == 'Name') {
                hasName = true;
                System.assertEquals('STRING', field.type);
                System.assert(field.isRequired, 'Name field should be required');
            }
            if (field.apiName == 'Industry') {
                hasIndustry = true;
                System.assertEquals('PICKLIST', field.type);
                System.assert(!field.isRequired, 'Industry field should not be required');
            }
        }
        
        System.assert(hasName, 'Should include Name field');
        System.assert(hasIndustry, 'Should include Industry field');
    }
    
    @IsTest
    static void testPreviewExcelData() {
        // Create test CSV data
        String csvData = 'Name,Industry,Phone\n' +
                         'Test Account 1,Technology,************\n' +
                         'Test Account 2,Finance,************\n';
        
        String base64Data = EncodingUtil.base64Encode(Blob.valueOf(csvData));
        
        Test.startTest();
        List<Map<String, String>> preview = ExcelImportController.previewExcelData(base64Data, 'test.csv', 10);
        Test.stopTest();
        
        System.assertEquals(2, preview.size(), 'Should return 2 rows');
        System.assertEquals('Test Account 1', preview[0].get('Name'));
        System.assertEquals('Technology', preview[0].get('Industry'));
        System.assertEquals('Test Account 2', preview[1].get('Name'));
        System.assertEquals('Finance', preview[1].get('Industry'));
    }
    
    @IsTest
    static void testImportExcelData() {
        // Create test CSV data
        String csvData = 'Name,Industry,Phone\n' +
                         'Imported Account,Technology,************\n';
        
        String base64Data = EncodingUtil.base64Encode(Blob.valueOf(csvData));
        
        // Create field mappings
        Map<String, String> mappings = new Map<String, String>();
        mappings.put('Name', 'Name');
        mappings.put('Industry', 'Industry');
        mappings.put('Phone', 'Phone');
        
        Test.startTest();
        ExcelImportController.ImportResult result = ExcelImportController.importExcelData(
            base64Data, 
            'test.csv', 
            'Account', 
            mappings
        );
        Test.stopTest();
        
        System.assert(result.isSuccess, 'Import should be successful');
        System.assertEquals(1, result.successCount, 'Should have 1 success');
        System.assertEquals(0, result.errorCount, 'Should have 0 errors');
        System.assertEquals(1, result.processedRecords.size(), 'Should have 1 processed record');
        
        // Verify the record was created
        ExcelImportController.ProcessedRecord record = result.processedRecords[0];
        System.assert(record.isSuccess, 'Record should be successful');
        System.assert(String.isNotBlank(record.recordId), 'Record should have ID');
        
        // Query and verify the created account
        Account importedAccount = [
            SELECT Id, Name, Industry, Phone 
            FROM Account 
            WHERE Id = :record.recordId
        ];
        System.assertEquals('Imported Account', importedAccount.Name);
        System.assertEquals('Technology', importedAccount.Industry);
        System.assertEquals('************', importedAccount.Phone);
    }
    
    @IsTest
    static void testImportExcelDataWithErrors() {
        // Create test CSV data with missing required field
        String csvData = 'Industry,Phone\n' +  // Missing Name column
                         'Technology,************\n';
        
        String base64Data = EncodingUtil.base64Encode(Blob.valueOf(csvData));
        
        // Create field mappings
        Map<String, String> mappings = new Map<String, String>();
        mappings.put('Industry', 'Industry');
        mappings.put('Phone', 'Phone');
        
        Test.startTest();
        ExcelImportController.ImportResult result = ExcelImportController.importExcelData(
            base64Data, 
            'test.csv', 
            'Account', 
            mappings
        );
        Test.stopTest();
        
        System.assert(result.isSuccess, 'Import process should complete');
        System.assertEquals(0, result.successCount, 'Should have 0 successes');
        System.assertEquals(1, result.errorCount, 'Should have 1 error');
        
        // Verify error record
        ExcelImportController.ProcessedRecord record = result.processedRecords[0];
        System.assert(!record.isSuccess, 'Record should have failed');
        System.assert(String.isNotBlank(record.errorMessage), 'Should have error message');
    }
    
    @IsTest
    static void testDownloadTemplate() {
        Test.startTest();
        String templateData = ExcelImportController.downloadTemplate(
            'Account', 
            new List<String>{'Name', 'Industry', 'Phone'}
        );
        Test.stopTest();
        
        // Decode and verify template
        String decoded = EncodingUtil.base64Decode(templateData).toString();
        System.assert(decoded.contains('Name,Industry,Phone'), 'Should contain headers');
        System.assert(decoded.contains('Sample_Name'), 'Should contain sample data');
    }
    
    @IsTest
    static void testEmptyFile() {
        String emptyCsv = '';
        String base64Data = EncodingUtil.base64Encode(Blob.valueOf(emptyCsv));
        
        Test.startTest();
        ExcelImportController.ImportResult result = ExcelImportController.importExcelData(
            base64Data, 
            'empty.csv', 
            'Account', 
            new Map<String, String>()
        );
        Test.stopTest();
        
        System.assert(!result.isSuccess, 'Should fail for empty file');
        System.assert(result.errorMessage.contains('No data found'), 'Should have appropriate error message');
    }
}