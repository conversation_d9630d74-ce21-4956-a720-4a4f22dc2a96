/**
 * Created by wd978071 on 05-09-2025.
 */

global class BootDev implements Database.Batchable{
	global Database.QueryLocator start(Database.BatchableContext BC){
		return Database.getQueryLocator('SELECT Id,Name FROM Account');
	}

	global void execute(Database.BatchableContext BC, List<Account> scope){
		List<Account> accToUpdate = new List<Account>();
		for(SObject obj : scope){
			Account acc = (Account) obj;
			acc.Name= 'Test Bulk Update';
			acc.BillingState = 'Testing Billing Status';
			accToUpdate.add(acc);
		}

		if(!accToUpdate.isEmpty()){
			update  accToUpdate;
		}
	}

	global void finish(Database.BatchableContext BC){
		// we can add the Messaging here or send the email
	}

	global void execute(Database.BatchableContext context, List<Object> records) {
	}
}

