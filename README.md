# Excel Importer for Salesforce

A comprehensive Lightning Web Component (LWC) that allows users to import Excel/CSV data into Salesforce objects with dynamic field mapping, modern UI/UX, and robust error handling.

## Features

- **Multi-step Wizard Interface**: Guided import process with progress indicator
- **Dynamic Object Selection**: Choose any Salesforce object to import data into
- **Drag & Drop File Upload**: Modern file upload with drag-and-drop support
- **Intelligent Field Mapping**: Auto-mapping capabilities with manual override
- **Data Preview**: Preview data before importing with validation
- **Batch Processing**: Configurable batch sizes for large imports
- **Comprehensive Error Reporting**: Detailed error messages with row-level tracking
- **Export Results**: Download import results as CSV
- **Template Download**: Download Excel/CSV templates for data preparation

## Component Structure

```
excelImporter/
├── excelImporter.html          # Main component template
├── excelImporter.js            # Main component logic
├── excelImporter.css           # Main component styles
├── excelImporter.js-meta.xml   # Component configuration
├── __tests__/
│   └── excelImporter.test.ts   # Unit tests
└── fileUploadStep/             # Step 1: File Upload
    ├── fileUploadStep.html
    ├── fileUploadStep.js
    ├── fileUploadStep.css
    └── fileUploadStep.js-meta.xml
fieldMapper/                    # Step 2: Field Mapping
    ├── fieldMapper.html
    ├── fieldMapper.js
    ├── fieldMapper.css
    └── fieldMapper.js-meta.xml
dataPreview/                    # Step 3: Data Preview
    ├── dataPreview.html
    ├── dataPreview.js
    ├── dataPreview.css
    └── dataPreview.js-meta.xml
importResults/                  # Step 4: Results
    ├── importResults.html
    ├── importResults.js
    ├── importResults.css
    └── importResults.js-meta.xml
```

## Apex Controller

### ExcelImportController.cls

Main Apex controller providing backend functionality:

- `importExcelData`: Processes Excel/CSV data and creates Salesforce records
- `getAvailableObjects`: Returns list of importable objects
- `getObjectFields`: Returns fields for a specific object
- `previewExcelData`: Returns preview of Excel data
- `downloadTemplate`: Generates CSV template for download

### Key Features

- Dynamic object creation using Salesforce Schema API
- Type conversion for all field types
- Comprehensive error handling
- Governor limit awareness
- Batch processing support

## Installation

1. Deploy all components to your Salesforce org:
   ```bash
   sfdx force:source:deploy -p src/classes/ExcelImportController.cls
   sfdx force:source:deploy -p src/classes/ExcelImportControllerTests.cls
   sfdx force:source:deploy -p src/lwc/excelImporter
   ```

2. Or deploy the entire package:
   ```bash
   sfdx force:source:deploy -x package.xml
   ```

## Usage

1. Add the `excelImporter` component to any Lightning page:
   - App Page
   - Home Page
   - Record Page
   - Custom Tab

2. Configure component properties:
   - **Title**: Component title (default: "Excel Data Importer")
   - **Show Object Selection**: Whether to show object picker (default: true)
   - **Allowed File Types**: Comma-separated file extensions (default: .csv,.xlsx,.xls)

3. Follow the wizard steps:
   - **Step 1**: Upload Excel/CSV file and select target object
   - **Step 2**: Map Excel columns to Salesforce fields
   - **Step 3**: Preview data and configure import options
   - **Step 4**: View import results and download report

## File Format Support

- CSV files (recommended)
- Excel files (.xlsx, .xls) - converted to CSV for processing

## Field Types Supported

- String, ID, Textarea
- Email, URL, Phone
- Number, Currency, Percent
- Boolean
- Date, DateTime
- Picklist (values must match exactly)

## Error Handling

The component provides detailed error reporting:

- **File Level**: Invalid file type, size limits
- **Row Level**: Validation errors, required field missing
- **Field Level**: Type conversion errors, format validation

## Limitations

- Maximum file size: 10MB
- Excel files are converted to CSV for processing
- Complex field types (lookup, master-detail) require additional configuration
- Governor limits apply for large datasets

## Security

- All inputs are validated
- FLS (Field Level Security) enforced
- CRUD (Create, Read, Update, Delete) permissions checked
- No sensitive data logged

## Future Enhancements

1. **Advanced Excel Processing**: Direct Excel parsing without CSV conversion
2. **Lookup Field Support**: Intelligent lookup field mapping
3. **Scheduled Imports**: Asynchronous import processing
4. **Saved Mappings**: Save and reuse field mapping configurations
5. **Data Transformation**: Apply formulas and transformations during import
6. **Duplicate Management**: Built-in duplicate detection and handling
7. **Progress Tracking**: Real-time progress updates for large imports

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Support

For issues and feature requests, please create an issue in the repository.

## License

This project is licensed under the MIT License.